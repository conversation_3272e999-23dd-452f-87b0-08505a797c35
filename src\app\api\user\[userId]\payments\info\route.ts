import { NextRequest, NextResponse } from "next/server";
import { createAdminClient } from "@/services/supabase/server";
import { formatPaymentMethodName } from "@/utils/payment-method-formatter";

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const supabase = await createAdminClient();
    
    const resolvedParams = await Promise.resolve(params);
    const { userId } = resolvedParams;

    if (!userId) {
      return NextResponse.json(
        { error: "ID de usuário inválido" },
        { status: 400 }
      );
    }

    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData?.session) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    const { data: student, error: studentError } = await supabase
      .from("students")
      .select(`
        id,
        payment_status,
        last_payment_date,
        next_payment_due
      `)
      .eq("user_id", userId)
      .single();

    if (studentError && studentError.code !== 'PGRST116') {
      console.error("Erro ao buscar dados do estudante:", studentError);
      return NextResponse.json(
        { error: "Erro ao buscar dados do estudante" },
        { status: 500 }
      );
    }

    if (!student) {
      return NextResponse.json(
        { error: "Estudante não encontrado" },
        { status: 404 }
      );
    }

    // Buscar membership ativo e plano para obter o valor real da mensalidade
    const { data: activeMembership, error: membershipError } = await supabase
      .from("memberships")
      .select(`
        id,
        status,
        plan_id,
        plans (
          id,
          title,
          pricing_config
        )
      `)
      .eq("student_id", student.id)
      .eq("status", "active")
      .single();

    if (membershipError && membershipError.code !== 'PGRST116') {
      console.error("Erro ao buscar membership ativo:", membershipError);
    }

    const { data: subscription, error: subscriptionError } = await supabase
      .from("subscriptions")
      .select(`
        id,
        status,
        current_period_start,
        current_period_end
      `)
      .eq("student_id", student.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    const { data: payments, error: paymentsError } = await supabase
      .from("payments")
      .select(`
        id,
        amount,
        currency,
        payment_method,
        paid_at
      `)
      .eq("student_id", student.id)
      .eq("status", "paid")
      .order('paid_at', { ascending: false })
      .limit(5);

    // Buscar próximo pagamento pendente, vencido ou aguardando confirmação para obter data de vencimento real
    const { data: nextPayment, error: nextPaymentError } = await supabase
      .from("payments")
      .select(`
        id,
        due_date,
        amount,
        status,
        overdue_date
      `)
      .eq("student_id", student.id)
      .in("status", ["pending", "overdue", "awaiting_confirmation"])
      .order('due_date', { ascending: true })
      .limit(1)
      .single();

    // Buscar último pagamento pago (independente do mês) para verificar se há pagamento recente
    const { data: lastPaidPayment, error: lastPaidPaymentError } = await supabase
      .from("payments")
      .select(`
        id,
        due_date,
        amount,
        status,
        paid_at
      `)
      .eq("student_id", student.id)
      .eq("status", "paid")
      .order('paid_at', { ascending: false })
      .limit(1)
      .single();

    if (paymentsError && paymentsError.code !== 'PGRST116') {
      console.error("Erro ao buscar pagamentos:", paymentsError);
    }

    let totalPaid = 0;
    let preferredMethod = "";
    const methodCounts: Record<string, number> = {};

    if (payments && payments.length > 0) {
      totalPaid = payments.reduce((total: number, payment: any) => {
        return total + Number(payment.amount || 0);
      }, 0);

      payments.forEach((payment: any) => {
        const method = payment.payment_method || 'desconhecido';
        methodCounts[method] = (methodCounts[method] || 0) + 1;
      });

      let maxCount = 0;
      Object.entries(methodCounts).forEach(([method, count]) => {
        if (count > maxCount) {
          maxCount = count;
          preferredMethod = method;
        }
      });
    }

    // Calcular valor da mensalidade baseado no plano ativo ou último pagamento
    let subscriptionAmount = 0;

    if (activeMembership?.plans && typeof activeMembership.plans === 'object') {
      // Usar valor do plano ativo
      const plan = activeMembership.plans as { pricing_config?: any };
      const pricingConfig = plan.pricing_config;
      subscriptionAmount = Number(pricingConfig?.amount || 0);
    } else if (payments && payments.length > 0) {
      // Fallback para último pagamento se não houver plano ativo
      subscriptionAmount = Number(payments[0].amount || 0);
    } else {
      // Se não há plano ativo nem pagamentos, valor será 0
      subscriptionAmount = 0;
    }

    // Calcular status de pagamento baseado em dados reais
    let paymentStatus = 'em_dia';
    let displayPaymentDue = null;

    // Verificar se há um próximo pagamento pendente
    if (nextPayment?.due_date) {
      // Obter a data atual no timezone do Brasil
      const todayInBrazil = new Date().toLocaleDateString('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
      const [day, month, year] = todayInBrazil.split('/');
      const todayStr = `${year}-${month}-${day}`;

      // Verificar se há um pagamento pago recente que cobre o período atual
      // Se o último pagamento pago tem vencimento >= hoje, o estudante está em dia
      if (lastPaidPayment?.due_date && lastPaidPayment.due_date.split('T')[0] >= todayStr) {
        // O estudante pagou um período que ainda não venceu ou venceu hoje
        paymentStatus = 'em_dia';
        displayPaymentDue = nextPayment.due_date; // Mostrar o próximo pagamento pendente
      } else {
        // Usar a lógica normal do próximo pagamento pendente
        // Extrair apenas a parte da data do vencimento (YYYY-MM-DD)
        const dueDateStr = nextPayment.due_date.split('T')[0];

        // Verificar o status do pagamento no banco de dados
        if (nextPayment.status === 'overdue') {
          paymentStatus = 'atrasado'; // Pagamento marcado como vencido
        } else if (nextPayment.status === 'pending') {
          // Só considerar atrasado se o status no banco for 'overdue' ou se tiver overdue_date preenchido
          if (nextPayment.overdue_date) {
            paymentStatus = 'atrasado'; // Pagamento pendente com overdue_date preenchido
          } else {
            paymentStatus = 'pendente'; // Pagamento pendente mas ainda não venceu
          }
        } else if (nextPayment.status === 'awaiting_confirmation') {
          paymentStatus = 'pendente'; // Pagamento aguardando confirmação do admin
        } else if (nextPayment.status === 'paid') {
          paymentStatus = 'em_dia'; // Pagamento já realizado
        } else {
          // Para outros status, usar a lógica baseada apenas na data
          if (dueDateStr < todayStr) {
            paymentStatus = 'atrasado';
          } else {
            paymentStatus = 'pendente';
          }
        }

        displayPaymentDue = nextPayment.due_date;
      }
    } else {
      // Não há próximo pagamento pendente
      // Verificar se há um pagamento pago recente que ainda não venceu
      if (lastPaidPayment?.due_date) {
        const todayInBrazil = new Date().toLocaleDateString('pt-BR', {
          timeZone: 'America/Sao_Paulo',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
        const [day, month, year] = todayInBrazil.split('/');
        const todayStr = `${year}-${month}-${day}`;

        if (lastPaidPayment.due_date.split('T')[0] >= todayStr) {
          // O estudante pagou um período que ainda não venceu ou venceu hoje
          paymentStatus = 'em_dia';
          displayPaymentDue = lastPaidPayment.due_date; // Usar a data do pagamento pago
        } else {
          // Pagamento pago já venceu, usar fallback do estudante
          if (student?.payment_status === 'overdue') {
            paymentStatus = 'atrasado';
          } else if (student?.payment_status === 'pending') {
            paymentStatus = 'pendente';
          }
          displayPaymentDue = student?.next_payment_due;
        }
      } else {
        // Fallback para status do estudante se não houver pagamentos
        if (student?.payment_status === 'overdue') {
          paymentStatus = 'atrasado';
        } else if (student?.payment_status === 'pending') {
          paymentStatus = 'pendente';
        }
        displayPaymentDue = student?.next_payment_due;
      }
    }

    const paymentInfo = {
      payment_status: paymentStatus,
      total_paid: totalPaid,
      subscription_amount: subscriptionAmount,
      last_payment_date: student?.last_payment_date || null,
      next_payment_due: displayPaymentDue,
      subscription_status: subscription?.status || 'inactive',
      current_period_start: subscription?.current_period_start || null,
      current_period_end: subscription?.current_period_end || null,
      preferred_method: formatPaymentMethodName(preferredMethod) || 'PIX',
      preferred_method_slug: preferredMethod || 'pix'
    };

    return NextResponse.json(paymentInfo);
  } catch (error) {
    console.error("Erro ao processar solicitação:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
} 