/**
 * Serviço para processamento de pagamentos e despesas em atraso
 * Usado pelos cron jobs para automatizar o processamento de atrasos
 */

import { createClient } from '@/services/supabase/server'

export interface OverdueProcessingResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    paymentsUpdated: number
    expensesUpdated: number
    errors: string[]
  }
}

export interface PaymentOverdueResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    updated: number
    errors: string[]
    processedPayments: Array<{
      id: string
      student_id: string
      amount: number
      due_date: string
      overdue_days: number
    }>
  }
}

export interface ExpenseOverdueResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    updated: number
    errors: string[]
    processedExpenses: Array<{
      id: string
      supplier_name: string
      amount: number
      due_date: string
      overdue_days: number
    }>
  }
}



/**
 * Processa pagamentos em atraso usando função do Supabase
 * Atualiza status para 'overdue' e define overdue_date
 */
export async function processOverduePayments(tenantId?: string): Promise<PaymentOverdueResult> {
  try {
    const supabase = await createClient()

    console.log(`🔄 Processando pagamentos em atraso para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Chamar função do Supabase para processar pagamentos em atraso
    const { data: result, error } = await supabase.rpc('process_overdue_payments_v2', {
      p_tenant_id: tenantId || null
    })

    if (error) {
      console.error('❌ Erro ao chamar função do Supabase:', error)
      return {
        success: false,
        error: `Erro ao processar pagamentos: ${error.message}`
      }
    }

    if (!result || !result.success) {
      console.error('❌ Função do Supabase retornou erro:', result?.error)
      return {
        success: false,
        error: result?.error || 'Erro desconhecido na função do Supabase'
      }
    }

    const data = result.data
    console.log(`✅ Processamento concluído: ${data.updated}/${data.totalProcessed} pagamentos atualizados`)

    if (data.errors && data.errors.length > 0) {
      console.error('❌ Erros durante processamento:', data.errors)
    }

    if (data.processedPayments && data.processedPayments.length > 0) {
      console.log('� Pagamentos processados:')
      data.processedPayments.forEach((payment: any) => {
        console.log(`  - ${payment.student_name}: ${payment.overdue_days} dias em atraso`)
      })
    }

    return {
      success: true,
      data: {
        totalProcessed: data.totalProcessed || 0,
        updated: data.updated || 0,
        errors: data.errors || [],
        processedPayments: (data.processedPayments || []).map((payment: any) => ({
          id: payment.id,
          student_id: payment.student_id,
          amount: parseFloat(payment.amount),
          due_date: payment.due_date,
          overdue_days: payment.overdue_days
        }))
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar pagamentos em atraso:', error)

    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa despesas em atraso usando função do Supabase
 * Atualiza status para 'overdue'
 */
export async function processOverdueExpenses(tenantId?: string): Promise<ExpenseOverdueResult> {
  try {
    const supabase = await createClient()

    console.log(`🔄 Processando despesas em atraso para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Chamar função do Supabase para processar despesas em atraso
    const { data: result, error } = await supabase.rpc('process_overdue_expenses_v2', {
      p_tenant_id: tenantId || null
    })

    if (error) {
      console.error('❌ Erro ao chamar função do Supabase:', error)
      return {
        success: false,
        error: `Erro ao processar despesas: ${error.message}`
      }
    }

    if (!result || !result.success) {
      console.error('❌ Função do Supabase retornou erro:', result?.error)
      return {
        success: false,
        error: result?.error || 'Erro desconhecido na função do Supabase'
      }
    }

    const data = result.data
    console.log(`✅ Processamento concluído: ${data.updated}/${data.totalProcessed} despesas atualizadas`)

    if (data.errors && data.errors.length > 0) {
      console.error('❌ Erros durante processamento:', data.errors)
    }

    if (data.processedExpenses && data.processedExpenses.length > 0) {
      console.log('📋 Despesas processadas:')
      data.processedExpenses.forEach((expense: any) => {
        console.log(`  - ${expense.supplier_name}: ${expense.overdue_days} dias em atraso`)
      })
    }

    return {
      success: true,
      data: {
        totalProcessed: data.totalProcessed || 0,
        updated: data.updated || 0,
        errors: data.errors || [],
        processedExpenses: (data.processedExpenses || []).map((expense: any) => ({
          id: expense.id,
          supplier_name: expense.supplier_name,
          amount: parseFloat(expense.amount),
          due_date: expense.due_date,
          overdue_days: expense.overdue_days
        }))
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar despesas em atraso:', error)

    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa tanto pagamentos quanto despesas em atraso
 */
export async function processAllOverdue(tenantId?: string): Promise<OverdueProcessingResult> {
  try {
    console.log(`🚀 Iniciando processamento completo de atrasos...`)

    const [paymentsResult, expensesResult] = await Promise.all([
      processOverduePayments(tenantId),
      processOverdueExpenses(tenantId)
    ])

    const errors: string[] = []
    
    if (!paymentsResult.success) {
      errors.push(`Pagamentos: ${paymentsResult.error}`)
    }
    
    if (!expensesResult.success) {
      errors.push(`Despesas: ${expensesResult.error}`)
    }

    // Combinar erros dos processamentos individuais
    if (paymentsResult.data?.errors) {
      errors.push(...paymentsResult.data.errors)
    }
    
    if (expensesResult.data?.errors) {
      errors.push(...expensesResult.data.errors)
    }

    const totalProcessed = (paymentsResult.data?.totalProcessed || 0) + (expensesResult.data?.totalProcessed || 0)
    const paymentsUpdated = paymentsResult.data?.updated || 0
    const expensesUpdated = expensesResult.data?.updated || 0

    console.log(`✅ Processamento completo finalizado:`)
    console.log(`  - Total processados: ${totalProcessed}`)
    console.log(`  - Pagamentos atualizados: ${paymentsUpdated}`)
    console.log(`  - Despesas atualizadas: ${expensesUpdated}`)
    console.log(`  - Erros: ${errors.length}`)

    return {
      success: paymentsResult.success && expensesResult.success,
      data: {
        totalProcessed,
        paymentsUpdated,
        expensesUpdated,
        errors
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico no processamento completo:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}
