-- Migration: Fix Recurring Payment Creation Timing
-- Data: 2025-01-25
-- Descrição: Corrige a lógica de criação automática de pagamentos recorrentes
--           para não gerar o próximo pagamento quando o atual é pago antecipadamente

-- Função corrigida para criar próximo pagamento recorrente
-- Agora verifica se o pagamento foi feito dentro do período correto
CREATE OR REPLACE FUNCTION public.create_next_recurring_payment(p_paid_payment_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SET search_path TO ''
AS $function$
DECLARE
  v_membership_id UUID;
  v_tenant_id UUID;
  v_student_id UUID;
  v_plan_title TEXT;
  v_plan_amount NUMERIC;
  v_pricing_type TEXT;
  v_frequency TEXT;
  v_currency TEXT;
  v_paid_due_date DATE;
  v_paid_at TIMESTAMP;
  v_current_date DATE;
  v_next_due_date DATE;
  v_calculated_due_date DATE;
  v_payment_id UUID;
  v_result JSONB;
BEGIN
  -- Validações básicas
  IF p_paid_payment_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'ID do pagamento é obrigatório'
    );
  END IF;

  -- Buscar informações do pagamento pago e da membership
  SELECT 
    p.membership_id,
    p.tenant_id,
    p.student_id,
    p.amount,
    p.currency,
    p.due_date,
    p.paid_at,
    pl.title,
    pl.pricing_config->>'type',
    COALESCE(pl.pricing_config->>'frequency', 'monthly')
  INTO v_membership_id, v_tenant_id, v_student_id, v_plan_amount, v_currency, 
       v_paid_due_date, v_paid_at, v_plan_title, v_pricing_type, v_frequency
  FROM public.payments p
  JOIN public.memberships m ON m.id = p.membership_id
  JOIN public.plans pl ON pl.id = m.plan_id
  WHERE p.id = p_paid_payment_id
    AND p.status = 'paid'
    AND p.payment_type = 'recurring';

  -- Verificar se o pagamento foi encontrado e é recorrente
  IF v_membership_id IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Pagamento recorrente pago não encontrado ou não é do tipo recorrente'
    );
  END IF;

  -- Verificar se é um plano recorrente
  IF v_pricing_type != 'recurring' THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Esta função é apenas para planos recorrentes'
    );
  END IF;

  -- Obter a data atual
  v_current_date := CURRENT_DATE;

  -- NOVA LÓGICA: Verificar se o pagamento foi feito antecipadamente
  -- Se a data atual é anterior à data de vencimento, não criar o próximo pagamento ainda
  IF v_current_date < v_paid_due_date THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Pagamento antecipado detectado. O próximo pagamento será criado automaticamente na data de vencimento.',
      'info', jsonb_build_object(
        'paid_due_date', v_paid_due_date,
        'current_date', v_current_date,
        'days_early', v_paid_due_date - v_current_date,
        'next_creation_date', v_paid_due_date
      )
    );
  END IF;

  -- Calcular a próxima data de vencimento baseada na frequência
  CASE v_frequency
    WHEN 'weekly' THEN
      v_next_due_date := v_paid_due_date + INTERVAL '1 week';
    WHEN 'month', 'monthly' THEN
      v_next_due_date := v_paid_due_date + INTERVAL '1 month';
    WHEN 'yearly', 'year' THEN
      v_next_due_date := v_paid_due_date + INTERVAL '1 year';
    ELSE
      -- Default para monthly
      v_next_due_date := v_paid_due_date + INTERVAL '1 month';
  END CASE;

  -- Aplicar configuração de data de vencimento do tenant
  -- Se a configuração for para um dia específico, ajustar a data
  v_calculated_due_date := public.calculate_due_date_for_tenant(v_tenant_id, v_next_due_date);

  -- Verificar se já existe um pagamento para a próxima data
  IF EXISTS (
    SELECT 1 FROM public.payments 
    WHERE membership_id = v_membership_id 
      AND payment_type = 'recurring'
      AND due_date = v_calculated_due_date
      AND status IN ('pending', 'awaiting_confirmation')
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Já existe um pagamento para a próxima data de vencimento'
    );
  END IF;

  -- Criar o próximo pagamento recorrente
  INSERT INTO public.payments (
    id,
    tenant_id,
    student_id,
    membership_id,
    amount,
    currency,
    status,
    payment_type,
    description,
    due_date,
    billing_cycle,
    metadata,
    created_at
  ) VALUES (
    gen_random_uuid(),
    v_tenant_id,
    v_student_id,
    v_membership_id,
    v_plan_amount,
    v_currency,
    'pending',
    'recurring',
    'Mensalidade - ' || v_plan_title,
    v_calculated_due_date,
    v_frequency,
    jsonb_build_object(
      'membership_id', v_membership_id,
      'plan_title', v_plan_title,
      'billing_cycle', v_frequency,
      'previous_payment_id', p_paid_payment_id,
      'auto_created', true,
      'created_at', NOW(),
      'payment_timing', 'on_time_or_late'
    ),
    NOW()
  ) RETURNING id INTO v_payment_id;

  -- Atualizar next_billing_date da membership para a data do pagamento criado
  UPDATE public.memberships 
  SET 
    next_billing_date = v_calculated_due_date,
    updated_at = NOW()
  WHERE id = v_membership_id;

  -- Retornar resultado
  RETURN jsonb_build_object(
    'success', true,
    'data', jsonb_build_object(
      'payment_id', v_payment_id,
      'membership_id', v_membership_id,
      'amount', v_plan_amount,
      'due_date', v_calculated_due_date,
      'next_billing_date', v_calculated_due_date,
      'frequency', v_frequency,
      'plan_title', v_plan_title,
      'previous_payment_id', p_paid_payment_id,
      'payment_timing', 'on_time_or_late',
      'created_at', NOW()
    )
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Erro interno: ' || SQLERRM
    );
END;
$function$;

-- Comentário explicativo sobre a mudança
COMMENT ON FUNCTION public.create_next_recurring_payment(uuid) IS 
'Função corrigida para criar próximo pagamento recorrente apenas quando o pagamento atual é feito no prazo ou em atraso. Pagamentos antecipados não geram automaticamente o próximo pagamento.';
